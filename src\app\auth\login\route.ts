import { NextRequest, NextResponse } from "next/server";
import { extractLoginParams, fetchBackendLoginUrl, isRedirectResponse, isValidRedirectPath } from "@/core/auth/lib/login-utils";

export async function GET(request: NextRequest): Promise<NextResponse> {
	try {
		const { redirectPath } = extractLoginParams(new URL(request.url));
		if (!isValidRedirectPath(redirectPath))
			return NextResponse.json({ error: "invalid_redirect", message: "Path de redirecionamento inválido" }, { status: 400 });
		const backendResponse = await fetchBackendLoginUrl(redirectPath);
		if (isRedirectResponse(backendResponse)) {
			const location = backendResponse.location;
			if (!location) throw new Error("URL de redirecionamento não encontrada");
			return NextResponse.redirect(location, 302);
		}
		console.log(backendResponse);
		return NextResponse.json({ error: "backend_error", message: "Erro na comunicação com o servidor de autenticação" }, { status: 500 });
	} catch (error) {
		console.error("Erro no redirecionamento para Keycloak:", error);
		return NextResponse.json(
			{ error: "keycloak_error", message: "Erro interno ao processar redirecionamento para autenticação" },
			{ status: 500 }
		);
	}
}
