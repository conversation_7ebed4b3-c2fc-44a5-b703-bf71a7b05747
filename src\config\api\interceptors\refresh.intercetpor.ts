import RefreshService from "@/core/auth/services/refresh-service";
import { AxiosInstance } from "axios";

export const setupRefreshInterceptor = (instance: AxiosInstance): void => {
	const refreshService = new RefreshService(instance);

	instance.interceptors.response.use(
		response => response,
		async error => {
			const originalRequest = error.config;
			if (
				error.response?.status === 401 &&
				!originalRequest._retry &&
				originalRequest.url &&
				!refreshService.isNonRefreshableEndpoint(originalRequest.url)
			) {
				try {
					return await refreshService.handleTokenRefresh(originalRequest);
				} catch (refreshError) {
					console.error("Token refresh failed:", refreshError);

					// Redireciona para o handler de login quando o refresh falha (apenas no cliente)
					if (typeof window !== "undefined") {
						const currentPath = window.location.pathname;
						const keycloakLoginUrl = new URL("/auth/login", window.location.origin);
						keycloakLoginUrl.searchParams.set("redirect", currentPath);
						window.location.href = keycloakLoginUrl.toString();
						return Promise.reject(new Error("Redirecionando para login..."));
					}

					if (refreshError instanceof Error) {
						return Promise.reject(refreshError);
					}
					return Promise.reject(new Error(String(refreshError)));
				}
			}
			return Promise.reject(error instanceof Error ? error : new Error(String(error)));
		}
	);
};
